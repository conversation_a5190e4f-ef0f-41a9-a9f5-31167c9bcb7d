<?php

namespace App\Http\Controllers;
use Inertia\Inertia;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;
use App\Models\Header;

class SettingController extends Controller
{
    public function index()
    {
        $headers = Header::all();
        return Inertia::render('setting',[
            'headers' => $headers,
        ]);
    }

    public function addHeader(Request $request)
    {
        $request->validate([
            'image' => 'required|image|max:2048',
            'deskripsi' => 'required|string',
            'title' => 'required|string'
        ]);

        $imagePath = $request->file('image')->store('header', 'public');

        Header::create([
            'title_header' => $request->input('title'),
            'header_images' => json_encode([$imagePath]), // Simpan sebagai array
            'header_deskripsi' => $request->input('deskripsi')
        ]);

        return redirect()->back()->with('success', 'Header berhasil ditambahkan!');
    }

}
