<?php

namespace App\Http\Controllers;
use Inertia\Inertia;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;
use App\Models\Header;

class SettingController extends Controller
{
    public function index()
    {
        $headers = Header::all();
        return Inertia::render('setting',[
            'headers' => $headers,
        ]);
    }

    public function addHeader(Request $request)
    {
        $request->validate([
            'image' => 'required|image|max:2048',
            'deskripsi' => 'required|string'
        ]);

        $imagePath = $request->file('image')->store('header', 'public');

        Header::create([
            'header_images' => [$imagePath], // Simpan sebagai array
            'header_deskripsi' => $request->input('deskripsi')
        ]);

        return redirect()->back()->with('success', 'Header berhasil ditambahkan!');
    }

}
